'use client'

import { useState, useEffect, useRef } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { WORD_PACKS } from '@/lib/genyus/config'
import { useNavigation } from '@/contexts/NavigationContext'
import { useTutorial } from '@/contexts/TutorialContext'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  isStreaming?: boolean
}

export default function GenyusPage() {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isAsking, setIsAsking] = useState(false)
  const [wordsRemaining, setWordsRemaining] = useState<number | null>(null)
  const [needsUpgrade, setNeedsUpgrade] = useState(false)
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [purchasing, setPurchasing] = useState(false)
  const [sharingMessageId, setSharingMessageId] = useState<string | null>(null)

  const inputRef = useRef<HTMLTextAreaElement>(null)
  const router = useRouter()
  const supabase = createSupabaseClient()
  const { hideNavigation, showNavigation } = useNavigation()
  const { hideTutorial, showTutorial, stopTutorial } = useTutorial()

  // Word pack configuration from config
  const wordPacksArray = [
    { id: 'starter', ...WORD_PACKS.STARTER },
    { id: 'pro', ...WORD_PACKS.PRO },
    { id: 'power', ...WORD_PACKS.POWER },
  ]

  useEffect(() => {
    // Hide navigation and completely disable tutorial when entering OnlyGenyus
    hideNavigation()
    stopTutorial() // Stop any active tutorial
    hideTutorial() // Hide tutorial overlay

    // Prevent body scroll and bounce on mobile (lighter approach)
    const originalStyle = {
      overflow: document.body.style.overflow,
      overscrollBehavior: document.body.style.overscrollBehavior
    }

    // Don't set overflow hidden - it breaks scrolling in the messages container
    document.body.style.overscrollBehavior = 'none'

    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/login?next=' + encodeURIComponent('/genyus'))
        return
      }
      setUser(user)
      await loadWordBalance(user.id)
      setLoading(false)
    }
    checkAuth()

    // Restore original styles and show navigation/tutorial when leaving OnlyGenyus
    return () => {
      document.body.style.overflow = originalStyle.overflow
      document.body.style.overscrollBehavior = originalStyle.overscrollBehavior
      showNavigation()
      showTutorial()
    }
  }, [router, supabase, hideNavigation, showNavigation, stopTutorial, hideTutorial, showTutorial])

  // Removed auto-scroll - let user control scrolling manually

  const loadWordBalance = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('genyus_user_words')
        .select('words_remaining')
        .eq('user_id', userId)
        .single()
      
      if (error && error.code !== 'PGRST116') {
        console.error('Error loading word balance:', error)
        return
      }
      
      if (data) {
        setWordsRemaining(data.words_remaining)
      } else {
        // Initialize user with free monthly allowance
        const { data: newBalance, error: insertError } = await supabase
          .from('genyus_user_words')
          .insert({
            user_id: userId,
            words_remaining: WORD_PACKS.FREE_MONTHLY,
            tier: 'free'
          })
          .select('words_remaining')
          .single()
        
        if (!insertError && newBalance) {
          setWordsRemaining(newBalance.words_remaining)
        }
      }
    } catch (error) {
      console.error('Error with word balance:', error)
    }
  }



  const handlePurchase = async (priceId: string) => {
    setPurchasing(true)
    try {
      const response = await fetch('/api/words/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ priceId })
      })

      const data = await response.json()
      if (response.ok && data.url) {
        window.location.href = data.url
      } else {
        console.error('Checkout error:', data.error)
        alert('Failed to create checkout session. Please try again.')
      }
    } catch (error) {
      console.error('Purchase error:', error)
      alert('Failed to initiate purchase. Please try again.')
    } finally {
      setPurchasing(false)
    }
  }

  const handleShareToTimeline = async (messageId: string) => {
    setSharingMessageId(messageId)

    try {
      // Find the question and answer pair
      const messageIndex = messages.findIndex(m => m.id === messageId)
      if (messageIndex === -1 || messageIndex === 0) return

      const answerMessage = messages[messageIndex]
      const questionMessage = messages[messageIndex - 1]

      if (answerMessage.type !== 'assistant' || questionMessage.type !== 'user') return

      const response = await fetch('/api/genyus/share', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: questionMessage.content,
          answer: answerMessage.content
        })
      })

      const data = await response.json()

      if (response.ok) {
        // Show success message with better styling
        const successMessage = document.createElement('div')
        successMessage.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-xl shadow-lg z-50 font-medium'
        successMessage.textContent = '✨ Shared to your timeline!'
        document.body.appendChild(successMessage)

        setTimeout(() => {
          successMessage.remove()
        }, 3000)
      } else {
        throw new Error(data.error || 'Failed to share')
      }
    } catch (error) {
      console.error('Share error:', error)
      // Show error message
      const errorMessage = document.createElement('div')
      errorMessage.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-xl shadow-lg z-50 font-medium'
      errorMessage.textContent = 'Failed to share. Please try again.'
      document.body.appendChild(errorMessage)

      setTimeout(() => {
        errorMessage.remove()
      }, 3000)
    } finally {
      setSharingMessageId(null)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim() || isAsking || !user) return

    const question = inputValue.trim()
    setInputValue('')
    setIsAsking(true)

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: question,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, userMessage])

    // Add streaming assistant message
    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      isStreaming: true
    }
    setMessages(prev => [...prev, assistantMessage])

    try {
      const response = await fetch('/api/genyus', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ question })
      })

      if (!response.ok) {
        const errorData = await response.json()
        if (response.status === 402) {
          setNeedsUpgrade(true)
          setMessages(prev => prev.slice(0, -1)) // Remove streaming message
          return
        }
        throw new Error(errorData.error || 'Failed to get response')
      }

      // Handle streaming response
      const reader = response.body?.getReader()
      if (!reader) throw new Error('No response body')

      const decoder = new TextDecoder()
      let accumulatedContent = ''

      const processStream = async () => {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value, { stream: true })
          accumulatedContent += chunk

          // Update the streaming message
          setMessages(prev => prev.map(msg =>
            msg.id === assistantMessage.id
              ? { ...msg, content: accumulatedContent }
              : msg
          ))

          // Yield to the event loop to prevent UI freezing
          await new Promise(resolve => setTimeout(resolve, 0))
        }
      }

      await processStream()

      // Mark as complete
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessage.id 
          ? { ...msg, isStreaming: false }
          : msg
      ))

      // Reload word balance
      await loadWordBalance(user.id)

    } catch (error) {
      console.error('Error asking Genyus:', error)
      setMessages(prev => prev.slice(0, -1)) // Remove streaming message
      setMessages(prev => [...prev, {
        id: (Date.now() + 2).toString(),
        type: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      }])
    } finally {
      setIsAsking(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading...</div>
      </div>
    )
  }

  if (!user) return null

  return (
    <div className="h-screen bg-white flex flex-col">
      {/* Simple Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Back Button */}
          <Link
            href="/write"
            className="text-gray-500 hover:text-gray-700 text-sm"
          >
            ← Back
          </Link>

          {/* Center: OnlyGenyus */}
          <div className="text-center">
            <h1 className="text-xl font-semibold text-black">OnlyGenyus</h1>
          </div>

          {/* Right: Word Count */}
          {wordsRemaining !== null && (
            <div className="text-right">
              <div className="text-sm font-bold text-black">
                {wordsRemaining.toLocaleString()} words
              </div>
              {wordsRemaining < 1000 && (
                <button
                  onClick={() => setShowPurchaseModal(true)}
                  className="text-xs text-blue-600 hover:text-blue-700 mt-1"
                >
                  {wordsRemaining === 0 ? 'Get Words' : 'Refill'}
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Main Chat Area - SOLID FIXED HEIGHT */}
      <div className="flex-1 flex flex-col max-w-4xl mx-auto w-full">
        {/* Messages Container - SOLID SCROLLABLE AREA */}
        <div
          className="messages-container flex-1 overflow-y-auto px-4 sm:px-6 py-6"
          style={{
            scrollBehavior: 'smooth',
            overscrollBehavior: 'contain'
          }}
        >
          {messages.length === 0 ? (
            /* Empty State */
            <div className="flex flex-col items-center justify-center h-full min-h-[400px] text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">Creativity amplified</h2>
              <p className="text-gray-500 font-mono text-sm max-w-md">
                Ask anything. Get answers that go beyond what any single AI can provide.
              </p>
            </div>
          ) : (
            /* Messages */
            <div className="space-y-8 max-w-3xl mx-auto">
              {messages.map((message) => (
                <div key={message.id} className="group">
                  {message.type === 'user' ? (
                    /* User Message */
                    <div className="flex justify-end">
                      <div className="max-w-[85%] bg-blue-600 text-white rounded-2xl px-5 py-3">
                        <div className="font-medium leading-relaxed whitespace-pre-wrap break-words overflow-wrap-anywhere">
                          {message.content}
                        </div>
                        <div className="text-xs text-blue-100 mt-2 opacity-75">
                          {message.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  ) : (
                    /* Assistant Message */
                    <div className="flex justify-start">
                      <div className="max-w-[95%]">
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                          </div>
                          <span className="text-sm font-medium text-gray-700">OnlyGenyus</span>
                          <span className="text-xs text-gray-400 flex items-center space-x-1">
                            <span>4 AI synthesis</span>
                            <span>•</span>
                            <span>{message.timestamp.toLocaleTimeString()}</span>
                          </span>
                        </div>
                        <div className="bg-gray-50 rounded-2xl px-5 py-4 border border-gray-100">
                          <div className="font-mono text-sm leading-relaxed text-gray-900 whitespace-pre-wrap break-words overflow-wrap-anywhere">
                            {message.content}
                            {message.isStreaming && (
                              <span className="inline-block w-2 h-4 bg-blue-500 ml-1 animate-pulse rounded-sm" />
                            )}
                          </div>
                        </div>

                        {/* Share Button - Only show for completed assistant messages */}
                        {!message.isStreaming && message.content && (
                          <div className="flex items-center justify-end mt-2 space-x-2">
                            <button
                              onClick={() => handleShareToTimeline(message.id)}
                              disabled={sharingMessageId === message.id}
                              className="inline-flex items-center space-x-1 px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
                            >
                              {sharingMessageId === message.id ? (
                                <>
                                  <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                                  <span>Sharing...</span>
                                </>
                              ) : (
                                <>
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                  </svg>
                                  <span>Share to Timeline</span>
                                </>
                              )}
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Fixed Input Area */}
        <div className="flex-shrink-0 border-t border-gray-200 bg-white px-4 sm:px-6 py-4 pb-20 shadow-lg">
          <div className="max-w-3xl mx-auto">
            <form onSubmit={handleSubmit} className="relative">
              <div className="relative flex items-end space-x-3">
                <div className="flex-1 relative">
                  <textarea
                    ref={inputRef}
                    value={inputValue}
                    onChange={(e) => {
                      setInputValue(e.target.value)
                      // Auto-resize for immersive typing
                      const textarea = e.target
                      textarea.style.height = 'auto'
                      textarea.style.height = Math.min(textarea.scrollHeight, e.target.value.trim() ? 300 : 120) + 'px'
                    }}
                    onKeyDown={handleKeyDown}
                    placeholder="Ask anything."
                    className="w-full resize-none border-2 border-gray-200 rounded-2xl px-5 py-4 pr-12 font-mono text-base placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-300 bg-white shadow-sm transition-all"
                    rows={1}
                    maxLength={8000}
                    disabled={isAsking || needsUpgrade}
                    style={{
                      minHeight: '56px',
                      maxHeight: inputValue.trim() ? '300px' : '120px', // Expand when typing
                      transition: 'max-height 0.2s ease-out'
                    }}
                  />
                  <div className="absolute right-3 bottom-3 text-xs text-gray-400 font-mono">
                    {inputValue.length}/8000
                  </div>
                </div>
                <button
                  type="submit"
                  disabled={!inputValue.trim() || isAsking || needsUpgrade}
                  className="flex-shrink-0 w-14 h-14 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white rounded-2xl transition-colors flex items-center justify-center disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                >
                  {isAsking ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  )}
                </button>
              </div>
            </form>

            {needsUpgrade && (
              <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-xl">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-amber-800 font-medium">
                      Out of words
                    </p>
                    <p className="text-sm text-amber-700 mt-1">
                      Keep OnlyGenyus flowing with the Starter Pack (50k words).
                    </p>
                    <button
                      onClick={() => setShowPurchaseModal(true)}
                      className="mt-2 text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      Buy Words Now →
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Purchase Modal */}
      {showPurchaseModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl p-8 w-full max-w-lg shadow-2xl">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-2xl font-semibold text-gray-900">Refill Words</h2>
                <p className="text-gray-500 text-sm mt-1">Choose your word pack</p>
              </div>
              <button
                onClick={() => setShowPurchaseModal(false)}
                className="text-gray-400 hover:text-gray-600 p-2 hover:bg-gray-100 rounded-xl transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <p className="text-gray-600 text-sm mb-8 bg-gray-50 p-4 rounded-xl">
              💡 Your free words reset monthly. Refill anytime to keep the creativity flowing.
            </p>

            <div className="space-y-4">
              {wordPacksArray.map((pack) => (
                <button
                  key={pack.id}
                  onClick={() => handlePurchase(pack.priceId)}
                  disabled={purchasing}
                  className="w-full p-5 border-2 border-gray-100 hover:border-blue-200 rounded-2xl hover:bg-blue-50/50 transition-all text-left disabled:opacity-50 group"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold text-gray-900 text-lg">{pack.label}</div>
                      <div className="font-mono text-sm text-gray-600 mt-1">
                        {pack.words.toLocaleString()} words
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-blue-600 text-xl">{pack.price}</div>
                      <div className="text-xs text-gray-500 font-mono">
                        ${(parseFloat(pack.price.slice(1)) / (pack.words / 1000)).toFixed(2)}/1k words
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>

            <div className="mt-8 text-center">
              <div className="flex items-center justify-center space-x-2 text-xs text-gray-500">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <span>Secure payment powered by Stripe</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
