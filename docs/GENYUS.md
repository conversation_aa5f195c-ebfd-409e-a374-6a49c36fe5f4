# OnlyGenyus - 4x Better AI Answers

## Overview

OnlyGenyus is OnlyDiary's AI chat feature that provides "4x better AI answers" through a sophisticated multi-model synthesis approach. It replaces the "Write a Book (Editor)" option in the Create menu and fits perfectly into the "Only" brand family (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Only<PERSON>udio, <PERSON><PERSON><PERSON><PERSON><PERSON>).

## Key Features

### 🤖 Multi-Model AI Synthesis
- **Pipeline**: 3 cheap generator models in parallel → 1 moderator model (DeepSeek-V3.1) fuses/edits to a single answer
- **Positioning**: "Get 4x better AI answers" - confident, specific messaging
- **Models**: OpenAI GPT-4o-mini, Google Gemini 1.5 Flash, Anthropic Claude 3 Haiku + DeepSeek V3.1 moderator

### 💰 Word-Based Monetization
- **Billing**: Word Meter system (sell words, not tokens)
- **Deduction**: Only the moderator's final visible words count
- **Free Tier**: 5,000 words monthly (resets automatically)
- **Paid Tiers**: Starter (50k), Pro (250k), Power (1M words)

### 🎨 Clean Chat Interface
- **Font**: Geist Mono for chat (Supabase-style)
- **Design**: Clean, crisp aesthetic matching OnlyDiary
- **Streaming**: Real-time response streaming
- **Mobile-first**: Responsive design

## Technical Implementation

### Frontend (`/genyus`)
- **Framework**: Next.js 15 App Router
- **Styling**: Tailwind CSS with Geist Mono font
- **Features**: Real-time streaming, word meter, purchase modal
- **Auth**: Supabase authentication required

### Backend APIs
- **`/api/genyus`**: Main chat endpoint (Edge runtime)
- **`/api/words/checkout`**: Stripe checkout for word purchases
- **`/api/words/webhook`**: Stripe webhook handler
- **`/api/words/reset-monthly`**: Cron job for monthly resets

### Database Schema
```sql
-- Core tables
genyus_user_words      -- User word balances and tiers
genyus_requests        -- Chat requests
genyus_provider_responses -- Raw AI responses (transparency)
genyus_answers         -- Final moderated answers
genyus_word_purchases  -- Stripe purchase records
```

### Word Counting Algorithm
- **Robust**: Uses `Intl.Segmenter` with regex fallback
- **Fair**: Bills only final visible words
- **Anti-abuse**: Strips code blocks to prevent word inflation
- **Consistent**: Deterministic across all environments

## Configuration

### Environment Variables
```bash
# AI Providers (for future implementation)
OPENAI_API_KEY=...
GOOGLE_API_KEY=...
ANTHROPIC_API_KEY=...
DEEPSEEK_API_KEY=...

# Stripe
STRIPE_SECRET_KEY=...
STRIPE_WEBHOOK_SECRET=...
NEXT_PUBLIC_STRIPE_PRICE_STARTER=price_...
NEXT_PUBLIC_STRIPE_PRICE_PRO=price_...
NEXT_PUBLIC_STRIPE_PRICE_POWER=price_...

# Supabase (existing)
NEXT_PUBLIC_SUPABASE_URL=...
SUPABASE_SERVICE_ROLE_KEY=...

# Optional
CRON_SECRET=... # For monthly reset endpoint
APP_ORIGIN=https://onlydiary.app
```

### Word Pack Pricing
- **Starter**: $9.99 for 50,000 words
- **Pro**: $29.99 for 250,000 words  
- **Power**: $99.99 for 1,000,000 words

## Current Status

### ✅ Production Ready Implementation
- [x] **Premium full-screen UI** (ChatGPT/Claude-style interface)
- [x] **Real AI provider integrations** (OpenAI, Google, Anthropic, DeepSeek)
- [x] **Multi-model synthesis pipeline**
- [x] **Streaming responses from DeepSeek moderator**
- [x] **Share to Timeline feature** (viral marketing built-in)
- [x] **OpenAI content moderation**
- [x] **Rate limiting** (30/min, 100/hour per user)
- [x] **Caching system** (in-memory, Redis-ready)
- [x] **Error handling and fallbacks**
- [x] **Admin monitoring API**
- [x] Word meter and balance tracking
- [x] Stripe checkout integration
- [x] Database schema and RLS policies
- [x] Purchase modal and word refill flow
- [x] Monthly reset functionality

### 🚧 Optional Enhancements
- [ ] Redis caching for production scaling
- [ ] Transparency panel showing individual AI responses
- [ ] Admin dashboard UI
- [ ] Referral bonus system
- [ ] Advanced analytics and insights

## Usage

1. **Access**: Navigate to `/write` → Click "OnlyGenyus"
2. **Chat**: Type questions in the premium full-screen interface
3. **Streaming**: Watch responses stream in real-time from DeepSeek
4. **Share**: Click "Share to Timeline" on any answer to showcase brilliant Q&As
5. **Word Tracking**: Monitor remaining words in compact top meter
6. **Purchase**: Click "Refill" when running low
7. **Billing**: Only final answer words are deducted from balance

## Key Features

### 🎨 **Premium Interface**
- **Full-screen experience** like ChatGPT/Claude
- **Sticky header** with backdrop blur
- **Empty state** with gradient icon and "Creativity amplified" messaging
- **Message bubbles** with OnlyGenyus branding and timestamps
- **Auto-expanding input** with character counter and send icon

### 🚀 **Share to Timeline**
- **Viral marketing built-in**: Users can share impressive Q&As to their timeline
- **Automatic formatting**: Creates beautiful diary entries with question/answer format
- **Free content**: Shared Q&As are always free to view (no paywall)
- **Brand attribution**: Each share includes OnlyGenyus branding and explanation
- **Social proof**: Demonstrates the power of 4x better AI answers to followers

## File Structure

```
app/
├── genyus/page.tsx              # Premium full-screen chat interface
├── api/genyus/
│   ├── route.ts                 # Chat API endpoint (production AI)
│   └── share/route.ts           # Share Q&A to timeline
├── api/words/
│   ├── checkout/route.ts        # Stripe checkout
│   ├── webhook/route.ts         # Stripe webhooks
│   └── reset-monthly/route.ts   # Monthly reset cron
├── api/admin/
│   └── genyus-stats/route.ts    # Admin monitoring API
└── write/page.tsx               # Updated create menu

lib/genyus/
├── config.ts                    # Configuration constants
├── word-count.ts               # Word counting utilities
├── providers.ts                # AI provider implementations
├── orchestrator.ts             # Main request orchestrator
├── moderation.ts               # OpenAI content moderation
├── cache.ts                    # Caching system
└── rate-limit.ts               # Rate limiting

supabase/migrations/
└── 20250127_genyus_tables.sql  # Database schema

.env.genyus.example             # Environment variables template
```

## Messaging Strategy

The feature uses confident, specific messaging throughout:
- **"Get 4x better AI answers"** (Create menu)
- **"Our custom AI model achieves up to 4x better answers"** (Main page)
- **"Ask OnlyGenyus anything for 4x better answers"** (Chat placeholder)
- **"OnlyGenyus Analysis"** / **"OnlyGenyus Expert Analysis"** (Response headers)

This positioning emphasizes mathematical confidence and superior quality over generic "AI synthesis" language, while maintaining brand consistency with the "Only" family.
