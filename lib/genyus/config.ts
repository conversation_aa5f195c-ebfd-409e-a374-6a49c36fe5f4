// Genyus Configuration

export const WORD_PACKS = {
  FREE_MONTHLY: 5000,      // Free monthly allowance
  STARTER: { 
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_STARTER || 'price_starter', 
    words: 50000, 
    label: "Starter",
    price: "$9.99"
  },
  PRO: { 
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_PRO || 'price_pro', 
    words: 250000, 
    label: "Pro",
    price: "$29.99"
  },
  POWER: { 
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_POWER || 'price_power', 
    words: 1000000, 
    label: "Power",
    price: "$99.99"
  },
};

export const REFERRAL_BONUS = 2000;
export const SHARE_BONUS = 1000;

// Generator system prompts - each model gets a different perspective
export const GEN_SYSTEM = "You are an expert assistant. Provide comprehensive, accurate answers with specific details and examples. Be thorough but concise.";

export const DIVERSIFY_PROMPTS = {
  steps: "Focus on practical implementation: Break this down into clear, actionable steps with specific details, timelines, and concrete examples. Include potential obstacles and how to overcome them.",
  analogies: "Focus on conceptual understanding: Use vivid analogies, metaphors, and real-world examples to explain complex concepts. Make abstract ideas concrete and relatable.",
  pitfalls: "Focus on critical analysis: Examine this from multiple angles, highlighting potential risks, common mistakes, alternative approaches, and edge cases. Consider what could go wrong and why."
};

// Enhanced moderator prompt for DeepSeek
export const MODERATOR_SYSTEM = `You are OnlyGenyus, an expert AI synthesizer that combines multiple AI perspectives into superior answers.

Your task: Analyze the USER QUESTION and the three MODEL ANSWERS (each with different strengths - practical steps, conceptual clarity, and critical analysis). Create ONE comprehensive answer that:

SYNTHESIS PROCESS:
1) Extract the most accurate and valuable insights from each response
2) Identify complementary information that can be merged for completeness
3) Resolve any contradictions by weighing evidence and noting uncertainties
4) Fill small knowledge gaps with widely-accepted facts (if relevant)
5) Organize information logically with clear structure (lists, steps, sections as appropriate)
6) Ensure the final answer is more comprehensive and useful than any individual response

QUALITY STANDARDS:
- Be thorough but concise - aim for depth over breadth
- Use specific examples and concrete details when helpful
- Maintain accuracy - if sources conflict, acknowledge uncertainty
- Format for readability with appropriate structure
- End with a brief summary if the answer is lengthy (8+ sentences)

Never mention the source models or that you're synthesizing responses. Present the final answer as your own expert knowledge.`;

// Rate limiting
export const RATE_LIMITS = {
  REQUESTS_PER_MINUTE: 30,
  REQUESTS_PER_HOUR: 100
};

// Timeouts (in milliseconds)
export const TIMEOUTS = {
  GENERATOR: 12000,  // 12 seconds per generator
  MODERATOR: 15000,  // 15 seconds for moderator
  TOTAL: 45000       // 45 seconds total
};

// Cache settings
export const CACHE_TTL = 60 * 15; // 15 minutes
