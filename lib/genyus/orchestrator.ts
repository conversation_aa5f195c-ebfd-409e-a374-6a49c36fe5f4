import { createClient } from "@supabase/supabase-js";
import { countChargeableWords } from "./word-count";
import { getCachedResponse, setCachedResponse } from "./cache";
import { moderateContent, shouldBlockContent, generateRefusalMessage } from "./moderation";
import {
  callOpen<PERSON><PERSON><PERSON>,
  callG<PERSON><PERSON>Flash,
  call<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  callDeepSeekModerator,
  type ProviderResponse
} from "./providers";

export interface GenyusRequest {
  userId: string;
  question: string;
  requestId?: string;
}

export interface GenyusResponse {
  stream: ReadableStream<string>;
  getFinalText: () => Promise<string>;
  getWordCount: () => Promise<number>;
  getMetadata: () => Promise<{
    requestId: string;
    cached: boolean;
    latency: number;
    providerResponses: ProviderResponse[];
    moderatorLatency: number;
    wordCount: number;
    safetyFlags?: any;
  }>;
}

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  );
}

export async function processGenyusRequest(request: GenyusRequest): Promise<GenyusResponse> {
  const { userId, question } = request;
  const supabase = createSupabaseServiceClient();
  
  // Check cache first
  const cachedResponse = await getCachedResponse(userId, question);
  if (cachedResponse) {
    return createCachedResponse(cachedResponse);
  }

  // Save request to database
  const { data: reqRow, error: reqErr } = await supabase
    .from("genyus_requests")
    .insert({ user_id: userId, question })
    .select()
    .single();

  if (reqErr) {
    throw new Error(`Failed to log request: ${reqErr.message}`);
  }

  const requestId = reqRow.id;
  let finalText = '';
  let wordCount = 0;
  let providerResponses: ProviderResponse[] = [];
  let moderatorLatency = 0;
  let safetyFlags: any = null;

  // Fan out to 3 generators in parallel
  const generatorPromises = [
    callOpenAIMini(question),
    callGeminiFlash(question),
    callClaudeHaiku(question), // uses Claude 3.5 Haiku via providers.ts
  ];

  const generatorResults = await Promise.allSettled(generatorPromises);
  
  // Extract successful responses
  providerResponses = generatorResults.map((result, index) => {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      // Create error response
      const providers = ['openai', 'google', 'anthropic'];
      const models = ['gpt-4o-mini', 'gemini-1.5-flash', process.env.ANTHROPIC_MODEL || 'claude-3-5-haiku-latest'];
      return {
        provider: providers[index],
        model: models[index],
        text: '',
        latency: 0,
        error: result.reason?.message || 'Unknown error'
      };
    }
  });

  // Store provider responses (fire and forget)
  storeProviderResponses(supabase, requestId, providerResponses);

  // Get successful responses for moderation
  const successfulResponses = providerResponses.filter(r => r.text && !r.error);

  // If no successful responses, return error
  if (successfulResponses.length === 0) {
    const errorText = "I'm experiencing technical difficulties with all AI providers. Please try again in a moment.";
    return createErrorResponse(errorText, requestId, providerResponses);
  }

  // Call DeepSeek moderator
  const moderatorStart = Date.now();
  const { stream, getFinalText, getMetadata } = await callDeepSeekModerator(question, successfulResponses);
  
  // Create response stream that handles post-processing
  const processedStream = new ReadableStream<string>({
    async start(controller) {
      const reader = stream.getReader();
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          finalText += value;
          controller.enqueue(value);
        }
      } catch (error) {
        controller.error(error);
      } finally {
        controller.close();
        
        // Post-processing after stream completes
        (async () => {
          try {
            const moderatorMeta = await getMetadata();
            moderatorLatency = moderatorMeta.latency;
            wordCount = countChargeableWords(finalText);

            // Safety moderation
            const moderation = await moderateContent(finalText);
            if (shouldBlockContent(moderation)) {
              safetyFlags = { flagged: true, categories: moderation.categories };
              // Don't deduct words for blocked content
              return;
            }

            // Deduct words atomically
            await deductWords(supabase, userId, wordCount);

            // Save final answer
            await supabase.from("genyus_answers").insert({
              request_id: requestId,
              moderator_model: "deepseek-chat",
              final_text: finalText,
              word_count: wordCount,
              latency_ms: moderatorLatency,
              tokens_in: moderatorMeta.tokensIn,
              tokens_out: moderatorMeta.tokensOut,
              safety_flags: safetyFlags,
            });

            // Cache the response
            await setCachedResponse(userId, question, finalText);

          } catch (error) {
            console.error('Post-processing error:', error);
          }
        })();
      }
    }
  });

  return {
    stream: processedStream,
    getFinalText: async () => finalText,
    getWordCount: async () => wordCount,
    getMetadata: async () => ({
      requestId,
      cached: false,
      latency: Date.now() - moderatorStart,
      providerResponses,
      moderatorLatency,
      wordCount,
      safetyFlags,
    }),
  };
}

// Helper functions
async function deductWords(supabase: any, userId: string, wordCount: number): Promise<void> {
  const { error } = await supabase.rpc('genyus_decrement_words', {
    p_user_id: userId,
    p_words: wordCount
  });
  
  if (error) {
    console.error('Word deduction error:', error);
    throw new Error('Failed to deduct words');
  }
}

async function storeProviderResponses(
  supabase: any, 
  requestId: string, 
  responses: ProviderResponse[]
): Promise<void> {
  try {
    const records = responses.map(response => ({
      request_id: requestId,
      provider: response.provider,
      model: response.model,
      latency_ms: response.latency,
      tokens_in: response.tokensIn,
      tokens_out: response.tokensOut,
      raw_text: response.text,
      normalized_text: response.text, // Could add normalization logic here
      error: response.error,
    }));

    await supabase.from("genyus_provider_responses").insert(records);
  } catch (error) {
    console.error('Failed to store provider responses:', error);
    // Don't throw - this is not critical
  }
}

function createCachedResponse(cachedText: string): GenyusResponse {
  const stream = new ReadableStream<string>({
    start(controller) {
      // Stream cached response word by word for consistent UX
      const words = cachedText.split(' ');
      let index = 0;
      
      const streamWord = () => {
        if (index < words.length) {
          controller.enqueue(words[index] + ' ');
          index++;
          setTimeout(streamWord, 30); // Fast streaming for cached content
        } else {
          controller.close();
        }
      };
      
      streamWord();
    }
  });

  return {
    stream,
    getFinalText: async () => cachedText,
    getWordCount: async () => countChargeableWords(cachedText),
    getMetadata: async () => ({
      requestId: 'cached',
      cached: true,
      latency: 0,
      providerResponses: [],
      moderatorLatency: 0,
      wordCount: countChargeableWords(cachedText),
    }),
  };
}

function createErrorResponse(
  errorText: string, 
  requestId: string, 
  providerResponses: ProviderResponse[]
): GenyusResponse {
  const stream = new ReadableStream<string>({
    start(controller) {
      controller.enqueue(errorText);
      controller.close();
    }
  });

  return {
    stream,
    getFinalText: async () => errorText,
    getWordCount: async () => 0, // Don't charge for errors
    getMetadata: async () => ({
      requestId,
      cached: false,
      latency: 0,
      providerResponses,
      moderatorLatency: 0,
      wordCount: 0,
    }),
  };
}
