import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { GENERATOR_SYSTEMS, DIVERSIFY_PROMPTS, TIMEOUTS } from './config';

// Provider response interface
export interface ProviderResponse {
  provider: string;
  model: string;
  text: string;
  latency: number;
  tokensIn?: number;
  tokensOut?: number;
  error?: string;
}

// Timeout wrapper for all provider calls
async function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Timeout')), timeoutMs);
  });
  
  return Promise.race([promise, timeoutPromise]);
}

// Diversify prompts for different generators
function diversify(question: string, flavor: keyof typeof DIVERSIFY_PROMPTS): string {
  const instruction = DIVERSIFY_PROMPTS[flavor];
  return `${question}\n\nInstruction: ${instruction}`;
}

// OpenAI GPT-4o-mini provider
export async function callOpenAIMini(question: string): Promise<ProviderResponse> {
  const startTime = Date.now();
  
  try {
    const openai = new OpenAI({ 
      apiKey: process.env.OPENAI_API_KEY!,
    });

    const response = await withTimeout(
      openai.chat.completions.create({
        model: "gpt-4o-mini",
        temperature: 0.7,
        max_tokens: 1500,
        messages: [
          { role: "system", content: GENERATOR_SYSTEMS.openai },
          { role: "user", content: diversify(question, "steps") }
        ],
      }),
      TIMEOUTS.GENERATOR
    );

    const latency = Date.now() - startTime;
    const rawText = response.choices[0]?.message?.content || "";

    // Clean and validate the response text
    const text = rawText
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
      .replace(/\*{3,}/g, '**') // Fix excessive asterisks
      .trim();

    return {
      provider: "openai",
      model: "gpt-4o-mini",
      text,
      latency,
      tokensIn: response.usage?.prompt_tokens,
      tokensOut: response.usage?.completion_tokens,
    };

  } catch (error) {
    const latency = Date.now() - startTime;
    return {
      provider: "openai",
      model: "gpt-4o-mini",
      text: "",
      latency,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Google Gemini 1.5 Flash provider
export async function callGeminiFlash(question: string): Promise<ProviderResponse> {
  const startTime = Date.now();
  
  try {
    // Using fetch since the Google AI SDK has compatibility issues in edge runtime
    const response = await withTimeout(
      fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${process.env.GOOGLE_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: `${GENERATOR_SYSTEMS.google}\n\nUser: ${diversify(question, "analogies")}`
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 1500,
          }
        })
      }),
      TIMEOUTS.GENERATOR
    );

    const data = await response.json();
    const latency = Date.now() - startTime;
    
    if (!response.ok) {
      throw new Error(data.error?.message || 'Google API error');
    }

    const rawText = data.candidates?.[0]?.content?.parts?.[0]?.text || "";

    // Clean and validate the response text
    const text = rawText
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
      .replace(/\*{3,}/g, '**') // Fix excessive asterisks
      .trim();

    return {
      provider: "google",
      model: "gemini-1.5-flash",
      text,
      latency,
      tokensIn: data.usageMetadata?.promptTokenCount,
      tokensOut: data.usageMetadata?.candidatesTokenCount,
    };

  } catch (error) {
    const latency = Date.now() - startTime;
    return {
      provider: "google",
      model: "gemini-1.5-flash",
      text: "",
      latency,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Anthropic Claude 3 Haiku provider
export async function callClaudeHaiku(question: string): Promise<ProviderResponse> {
  const startTime = Date.now();
  
  try {
    const anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY!,
    });

    const model = process.env.ANTHROPIC_MODEL || 'claude-3-5-haiku-latest';

    const response = await withTimeout(
      anthropic.messages.create({
        model,
        max_tokens: 1500,
        temperature: 0.7,
        system: GENERATOR_SYSTEMS.anthropic,
        messages: [
          { role: "user", content: diversify(question, "pitfalls") }
        ],
      }),
      TIMEOUTS.GENERATOR
    );

    const latency = Date.now() - startTime;
    const rawText = response.content[0]?.type === 'text' ? response.content[0].text : "";

    // Clean and validate the response text
    const text = rawText
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
      .replace(/\*{3,}/g, '**') // Fix excessive asterisks
      .trim();

    return {
      provider: "anthropic",
      model,
      text,
      latency,
      tokensIn: response.usage.input_tokens,
      tokensOut: response.usage.output_tokens,
    };

  } catch (error) {
    const latency = Date.now() - startTime;
    return {
      provider: "anthropic",
      model: "claude-3-haiku",
      text: "",
      latency,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// DeepSeek V3.1 direct call (streaming)
export async function callDeepSeekDirect(
  question: string,
  conversationHistory?: Array<{role: string, content: string}>
): Promise<{
  stream: ReadableStream<string>;
  getFinalText: () => Promise<string>;
  getMetadata: () => Promise<{ latency: number; tokensIn?: number; tokensOut?: number; }>;
}> {
  const startTime = Date.now();
  let finalText = '';
  let metadata = { latency: 0, tokensIn: 0, tokensOut: 0 };

  // Build messages array with conversation history
  const messages = [
    {
      role: 'system',
      content: `You are OnlyGenyus, an exceptionally brilliant AI assistant that provides superior answers by thinking deeply and creatively about every question.

Your mission: Deliver answers that are genuinely better than what users would get from any other chatbot.

APPROACH:
- Think comprehensively about all aspects of the question
- Provide specific, actionable insights with concrete examples
- Offer unique perspectives that others would miss
- Structure your response for maximum clarity and usefulness
- Include relevant details that add genuine value

QUALITY STANDARDS:
- Be thorough but concise - every sentence should add value
- Use proper formatting (headers, bullets, numbers) to enhance readability
- Provide specific examples and actionable steps when helpful
- Write in clear, error-free prose with professional polish
- Ensure your answer is more complete and insightful than typical AI responses

Make users think: "This is exactly what I needed - comprehensive, clear, and brilliantly insightful."`
    },
    ...(conversationHistory || []),
    { role: 'user', content: question }
  ];

  try {
    const response = await withTimeout(
      fetch('https://api.deepseek.com/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
        },
        body: JSON.stringify({
          model: process.env.DEEPSEEK_MODEL || 'deepseek-chat',
          messages,
          temperature: 0.4,
          max_tokens: 2000,
          stream: true,
        }),
      }),
      TIMEOUTS.MODERATOR
    );

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    const decoder = new TextDecoder();
    
    const stream = new ReadableStream<string>({
      async start(controller) {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n').filter(line => line.trim());

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content && typeof content === 'string') {
                    // Clean the content to remove any malformed characters
                    const cleanContent = content.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
                    finalText += cleanContent;
                    controller.enqueue(cleanContent);
                  }

                  // Capture usage data
                  if (parsed.usage) {
                    metadata.tokensIn = parsed.usage.prompt_tokens;
                    metadata.tokensOut = parsed.usage.completion_tokens;
                  }
                } catch (e) {
                  console.error('DeepSeek streaming parse error:', e);
                  // Skip malformed JSON
                }
              }
            }
          }
        } catch (error) {
          controller.error(error);
        } finally {
          metadata.latency = Date.now() - startTime;
          controller.close();
        }
      }
    });

    return {
      stream,
      getFinalText: async () => {
        // Final cleanup of the complete text
        return finalText
          .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
          .replace(/\*{3,}/g, '**') // Fix excessive asterisks
          .replace(/#{4,}/g, '###') // Fix excessive headers
          .replace(/\n{3,}/g, '\n\n') // Fix excessive line breaks
          .trim();
      },
      getMetadata: async () => metadata,
    };

  } catch (error) {
    // Fallback to non-streaming if streaming fails
    const fallbackText = `I apologize, but I'm experiencing technical difficulties. Please try again in a moment.`;
    finalText = fallbackText;
    metadata.latency = Date.now() - startTime;

    const stream = new ReadableStream<string>({
      start(controller) {
        controller.enqueue(fallbackText);
        controller.close();
      }
    });

    return {
      stream,
      getFinalText: async () => finalText,
      getMetadata: async () => metadata,
    };
  }
}


