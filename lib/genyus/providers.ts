import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { GENERATOR_SYSTEMS, DIVERSIFY_PROMPTS, TIMEOUTS } from './config';

// Provider response interface
export interface ProviderResponse {
  provider: string;
  model: string;
  text: string;
  latency: number;
  tokensIn?: number;
  tokensOut?: number;
  error?: string;
}

// Timeout wrapper for all provider calls
async function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Timeout')), timeoutMs);
  });
  
  return Promise.race([promise, timeoutPromise]);
}

// Diversify prompts for different generators
function diversify(question: string, flavor: keyof typeof DIVERSIFY_PROMPTS): string {
  const instruction = DIVERSIFY_PROMPTS[flavor];
  return `${question}\n\nInstruction: ${instruction}`;
}

// OpenAI GPT-4o-mini provider
export async function callOpenAIMini(question: string): Promise<ProviderResponse> {
  const startTime = Date.now();
  
  try {
    const openai = new OpenAI({ 
      apiKey: process.env.OPENAI_API_KEY!,
    });

    const response = await withTimeout(
      openai.chat.completions.create({
        model: "gpt-4o-mini",
        temperature: 0.7,
        max_tokens: 1500,
        messages: [
          { role: "system", content: GENERATOR_SYSTEMS.openai },
          { role: "user", content: diversify(question, "steps") }
        ],
      }),
      TIMEOUTS.GENERATOR
    );

    const latency = Date.now() - startTime;
    const text = response.choices[0]?.message?.content || "";
    
    return {
      provider: "openai",
      model: "gpt-4o-mini",
      text,
      latency,
      tokensIn: response.usage?.prompt_tokens,
      tokensOut: response.usage?.completion_tokens,
    };

  } catch (error) {
    const latency = Date.now() - startTime;
    return {
      provider: "openai",
      model: "gpt-4o-mini",
      text: "",
      latency,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Google Gemini 1.5 Flash provider
export async function callGeminiFlash(question: string): Promise<ProviderResponse> {
  const startTime = Date.now();
  
  try {
    // Using fetch since the Google AI SDK has compatibility issues in edge runtime
    const response = await withTimeout(
      fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${process.env.GOOGLE_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: `${GENERATOR_SYSTEMS.google}\n\nUser: ${diversify(question, "analogies")}`
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 1500,
          }
        })
      }),
      TIMEOUTS.GENERATOR
    );

    const data = await response.json();
    const latency = Date.now() - startTime;
    
    if (!response.ok) {
      throw new Error(data.error?.message || 'Google API error');
    }

    const text = data.candidates?.[0]?.content?.parts?.[0]?.text || "";
    
    return {
      provider: "google",
      model: "gemini-1.5-flash",
      text,
      latency,
      tokensIn: data.usageMetadata?.promptTokenCount,
      tokensOut: data.usageMetadata?.candidatesTokenCount,
    };

  } catch (error) {
    const latency = Date.now() - startTime;
    return {
      provider: "google",
      model: "gemini-1.5-flash",
      text: "",
      latency,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Anthropic Claude 3 Haiku provider
export async function callClaudeHaiku(question: string): Promise<ProviderResponse> {
  const startTime = Date.now();
  
  try {
    const anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY!,
    });

    const model = process.env.ANTHROPIC_MODEL || 'claude-3-5-haiku-latest';

    const response = await withTimeout(
      anthropic.messages.create({
        model,
        max_tokens: 1500,
        temperature: 0.7,
        system: GENERATOR_SYSTEMS.anthropic,
        messages: [
          { role: "user", content: diversify(question, "pitfalls") }
        ],
      }),
      TIMEOUTS.GENERATOR
    );

    const latency = Date.now() - startTime;
    const text = response.content[0]?.type === 'text' ? response.content[0].text : "";

    return {
      provider: "anthropic",
      model,
      text,
      latency,
      tokensIn: response.usage.input_tokens,
      tokensOut: response.usage.output_tokens,
    };

  } catch (error) {
    const latency = Date.now() - startTime;
    return {
      provider: "anthropic",
      model: "claude-3-haiku",
      text: "",
      latency,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// DeepSeek V3.1 moderator (streaming)
export async function callDeepSeekModerator(
  question: string, 
  responses: ProviderResponse[]
): Promise<{
  stream: ReadableStream<string>;
  getFinalText: () => Promise<string>;
  getMetadata: () => Promise<{ latency: number; tokensIn?: number; tokensOut?: number; }>;
}> {
  const startTime = Date.now();
  let finalText = '';
  let metadata = { latency: 0, tokensIn: 0, tokensOut: 0 };

  // Build moderator prompt
  const prompt = buildModeratorPrompt(question, responses);

  try {
    const response = await withTimeout(
      fetch('https://api.deepseek.com/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
        },
        body: JSON.stringify({
          model: process.env.DEEPSEEK_MODEL || 'deepseek-chat',
          messages: [
            { role: 'system', content: 'You are OnlyGenyus, a master synthesizer who creates breakthrough insights by combining contrarian innovation, systems thinking, and human psychology. Your answers are known for being surprisingly original yet immediately practical.' },
            { role: 'user', content: prompt }
          ],
          temperature: 0.6,
          max_tokens: 2000,
          stream: true,
        }),
      }),
      TIMEOUTS.MODERATOR
    );

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    const decoder = new TextDecoder();
    
    const stream = new ReadableStream<string>({
      async start(controller) {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n').filter(line => line.trim());

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    finalText += content;
                    controller.enqueue(content);
                  }
                  
                  // Capture usage data
                  if (parsed.usage) {
                    metadata.tokensIn = parsed.usage.prompt_tokens;
                    metadata.tokensOut = parsed.usage.completion_tokens;
                  }
                } catch (e) {
                  // Skip malformed JSON
                }
              }
            }
          }
        } catch (error) {
          controller.error(error);
        } finally {
          metadata.latency = Date.now() - startTime;
          controller.close();
        }
      }
    });

    return {
      stream,
      getFinalText: async () => finalText,
      getMetadata: async () => metadata,
    };

  } catch (error) {
    // Fallback to non-streaming if streaming fails
    const fallbackText = `I apologize, but I'm experiencing technical difficulties. Please try again in a moment.`;
    finalText = fallbackText;
    metadata.latency = Date.now() - startTime;

    const stream = new ReadableStream<string>({
      start(controller) {
        controller.enqueue(fallbackText);
        controller.close();
      }
    });

    return {
      stream,
      getFinalText: async () => finalText,
      getMetadata: async () => metadata,
    };
  }
}

// Build the enhanced moderator prompt
function buildModeratorPrompt(question: string, responses: ProviderResponse[]): string {
  const validResponses = responses.filter(r => r.text && !r.error);

  if (validResponses.length === 0) {
    return `Please provide a comprehensive, expert-level answer to this question: ${question}`;
  }

  let prompt = `USER QUESTION:\n${question}\n\n`;

  // Label responses by their optimization focus
  const focusLabels = [
    "COMPREHENSIVE RESPONSE (optimized for depth and completeness)",
    "STRUCTURED RESPONSE (optimized for clarity and actionability)",
    "INSIGHTFUL RESPONSE (optimized for originality and breakthrough thinking)"
  ];

  validResponses.forEach((response, index) => {
    const label = focusLabels[index] || `RESPONSE ${String.fromCharCode(65 + index)}`;
    prompt += `${label}:\n${response.text}\n\n`;
  });

  prompt += `SYNTHESIS TASK:
You are OnlyGenyus. Your mission is to create a single answer that is demonstrably BETTER than any individual response above.

SYNTHESIS PROCESS:
1) EXTRACT THE BEST: Identify the strongest insights, most useful details, and clearest explanations from each response.

2) FILL THE GAPS: Notice what important information is missing from all responses and add it based on your knowledge.

3) IMPROVE STRUCTURE: Organize the information in the most logical, readable way possible.

4) ENHANCE QUALITY: Refine explanations, add better examples, and improve clarity throughout.

5) ADD VALUE: Include additional insights or perspectives that would make the answer more complete and useful.

QUALITY REQUIREMENTS:
- Your answer must be MORE comprehensive than the comprehensive response
- Your answer must be MORE clear than the structured response
- Your answer must be MORE insightful than the insightful response
- Include specific examples, actionable steps, and concrete details
- Use proper formatting (headers, bullets, numbers) to enhance readability
- Ensure every sentence adds genuine value

SUCCESS CRITERIA:
The user should think: "This is exactly what I needed - comprehensive, clear, and insightful all at once."

Never mention that you're combining responses. Present your answer as authoritative expert knowledge.`;

  return prompt;
}
